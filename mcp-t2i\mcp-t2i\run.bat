@echo off

REM 获取脚本所在目录，并设为工作目录
cd /d "%~dp0"

REM 设置 MAAS_API_KEY 环境变量（临时，仅在当前会话有效）
set MAAS_API_KEY=sk-f6lKpILfroyVLHK-rUNHkA

REM 检查虚拟环境是否存在
if not exist ".venv\Scripts\activate.bat" (
    echo 错误：未找到虚拟环境 .venv，请先创建虚拟环境
    pause
    exit /b 1
)

REM 激活虚拟环境
call ".venv\Scripts\activate.bat"

REM 检查 uv 是否可用
where uv >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误：未找到 uv 命令，请确保已安装
    pause
    exit /b 1
)

REM 运行 main.py
uv run main.py

REM 可选：运行完成后暂停（调试用）
pause