apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: filebrower
  annotations:
    cert-manager.io/cluster-issuer: zerossl-prod
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    # nginx.ingress.kubernetes.io/affinity: "off"
    # nginx.ingress.kubernetes.io/load-balance: round_robin
    
    # #to avoid 504 Gateway Timeout
    # nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    # nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    # nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    # nginx.ingress.kubernetes.io/proxy-next-upstream-timeout: "600"
    # nginx.ingress.kubernetes.io/proxy-body-size: '50m'
    # nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
    # nginx.ingress.kubernetes.io/proxy-buffers-number: "4"

spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - filebrower.zw1u.paratera.com
  rules:
  - host: filebrower.zw1u.paratera.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: filebrower
            port:
              number: 80        