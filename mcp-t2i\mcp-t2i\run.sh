#!/bin/bash

# 获取脚本所在目录，并设为工作目录
cd "$(dirname "$0")" || exit 1

# 设置 MAAS_API_KEY 环境变量（临时，仅在当前会话有效）
export MAAS_API_KEY="sk-f6lKpILfroyVLHK-rUNHkA"

# 检查虚拟环境是否存在
if [ ! -f ".venv/bin/activate" ]; then
    echo "错误：未找到虚拟环境 .venv，请先运行：python3 -m venv .venv"
    exit 1
fi

# 激活虚拟环境
source ".venv/bin/activate"

# 检查 uv 是否可用
if ! command -v uv &> /dev/null; then
    echo "错误：未找到 uv 命令，请确保已安装"
    exit 1
fi

# 运行 main.py
uv run main.py

# 可选：运行完成后暂停（按任意键继续）
read -n 1 -s -r -p "按任意键继续..."