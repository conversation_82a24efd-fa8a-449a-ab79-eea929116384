apiVersion: apps/v1
kind: Deployment
metadata:
  name: filebrower
spec:
  replicas: 1
  selector:
    matchLabels:
      app: filebrower
  template:
    metadata:
      labels:
        app: filebrower
    spec:
      containers:
        - name: filebrowser
          image: filebrowser/filebrowser:v2.30.0
          args:
          - --address=0.0.0.0
          - --root=/data
          - --password=$2a$10$KfhkSEaa.UJbRk24FG7m8udlx90630OZvRvA8/ix4C6rcaeYHfGNe
          ports:
          -  containerPort: 80
          resources:
            limits:
              cpu: 2
              memory: 4Gi
          securityContext:
            runAsUser: 0
          volumeMounts:
            - name: data
              mountPath: /data
      volumes:
        - name: data
          persistentVolumeClaim:
            claimName: word-mcp-pvc  