apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: word-mcp
  annotations:
    cert-manager.io/cluster-issuer: zerossl-prod
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/affinity: "off"
    nginx.ingress.kubernetes.io/load-balance: round_robin
    
    #to avoid 504 Gateway Timeout
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-next-upstream-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-body-size: '50m'
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"

spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - word-mcp.zw1u.paratera.com
  rules:
  - host: word-mcp.zw1u.paratera.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: word-mcp
            port:
              number: 80        