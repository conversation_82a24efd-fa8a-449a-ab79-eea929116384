apiVersion: apps/v1
kind: Deployment
metadata:
  name: word-mcp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: word-mcp
  template:
    metadata:
      labels:
        app: word-mcp
    spec:
      containers:
      - name: word-mcp
        image: cr.zw1u.paratera.com/llm/word-mcp:latest
        # command: ["tail", "-f", "/dev/null"]
        ports:
        - containerPort: 5000
        resources:
          limits:
            cpu: 8
            memory: 16Gi
        envFrom:
          - secretRef:
              name: config-env
        tty: true
        volumeMounts:
        - name: word-mcp-volume
          mountPath: /data
      volumes:
      - name: word-mcp-volume
        persistentVolumeClaim:
          claimName: word-mcp-pvc          