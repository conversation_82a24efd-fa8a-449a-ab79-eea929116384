# 使用 Python 3.12 的 Debian Bookworm 基础镜像
FROM python:3.12-slim-bookworm

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN sed -i 's/deb.debian.org/mirrors.huaweicloud.com/g' /etc/apt/sources.list.d/debian.sources && \ 
    apt-get update && apt-get install -y \
    build-essential \
    libssl-dev \
    fontconfig \
    procps \
    vim \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装字体
COPY fonts /usr/share/fonts/custom/
RUN fc-cache -fv && mkdir -p /app    

# 设置环境变量指定字体路径
ENV FONT_PATH=/usr/share/fonts/custom/simhei.ttf

# 复制依赖文件
COPY requirements.txt .

RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple

# 升级 pip 和工具
RUN pip install --no-cache-dir --upgrade pip setuptools==80.0.0 wheel

# 安装 Python 依赖
RUN pip install --no-cache-dir \
    --use-pep517 \
    --no-build-isolation \
    -r requirements.txt 

ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 复制应用代码
COPY . .

# 暴露容器内部的 5000 端口
EXPOSE 5000



# 设置启动命令
CMD ["python", "app.py"]